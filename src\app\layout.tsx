// src/app/layout.tsx
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Providers from "./providers";
import { Toaster } from "@/components/ui/sonner"; // Importer Toaster
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { FileSignature, Handshake, ShieldCheck, ListChecks, AlertTriangle } from "lucide-react";
import { headers } from "next/headers";
import { NavbarWithSession } from "@/components/layout/navbar";

const inter = Inter({ subsets: ["latin"] });

const adminNavLinks = [
  { href: "/dashboard", label: "Tableau de Bord", icon: <span className="mr-3">📊</span> },
  { href: "/admin/utilisateurs", label: "Utilisateurs", icon: <span className="mr-3">👥</span> },
  { href: "/admin/audit-log", label: "Journal d'Audit", icon: <span className="mr-3">📝</span> },
  { href: "/admin/system-settings", label: "Paramètres Système", icon: <span className="mr-3">⚙️</span> },
  { href: "/lignes-garantie", label: "Lignes de Garantie", icon: <FileSignature className="mr-3 w-5 h-5" /> },
  { href: "/allocations", label: "Allocations Partenaires", icon: <Handshake className="mr-3 w-5 h-5" /> },
  { href: "/garanties", label: "Garanties", icon: <ShieldCheck className="mr-3 w-5 h-5" /> },
  { href: "/suivi/mainlevees", label: "Demandes de Mainlevée", icon: <ListChecks className="mr-3 w-5 h-5" /> },
  { href: "/suivi/mises-en-jeu", label: "Demandes de Mise en Jeu", icon: <AlertTriangle className="mr-3 w-5 h-5" /> },
];

export const metadata: Metadata = {
  title: "GesGar",
  description: "Gestion des Lignes de Garantie",
};

// Use environment-aware logging
if (process.env.NODE_ENV === 'development') {
  console.log("[RootLayout] Rendering on server...");
}
export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions);
  const headersList = await headers();
  const pathname = headersList.get("x-invoke-path") || headersList.get("x-pathname") || "";
  const isAuthPage = pathname.startsWith("/auth/");

  return (
    <html lang="fr">
      <body className={inter.className}>
        <Providers>
          {!isAuthPage && session ? (
            // Layout avec navbar et sidebar pour les utilisateurs connectés
            <div className="min-h-screen bg-muted/40">
              {/* Horizontal Navbar */}
              <NavbarWithSession />

              {/* Main layout with sidebar */}
              <div className="flex">
                {/* Sidebar for Configuration & Admin */}
                <aside className="hidden md:flex md:flex-col w-64 bg-background border-r">
                <div className="p-4 border-b">
                  <h1 className="text-2xl font-semibold text-primary">GesGar</h1>
                </div>
                <nav className="flex-1 p-4 space-y-2">
                  {session?.user?.role === "Administrateur" && (
                    <div>
                      <h2 className="px-3 py-2 text-xs font-semibold uppercase text-muted-foreground tracking-wider">
                        Administration
                      </h2>
                      <div className="space-y-1">
                        {adminNavLinks.map(link => (
                          <a key={link.href} href={link.href} className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                            {link.icon}
                            {link.label}
                          </a>
                        ))}
                      </div>
                    </div>
                  )}
                  <div>
                    <h2 className="px-3 py-2 text-xs font-semibold uppercase text-muted-foreground tracking-wider">
                      Configuration
                    </h2>
                    <div className="space-y-1">
                      <a href="/configuration/bailleurs" className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                        <span className="mr-3">🏛️</span>
                        Bailleurs de Fonds
                      </a>
                      <a href="/configuration/partenaires" className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                        <span className="mr-3">🏢</span>
                        Partenaires Financiers
                      </a>
                      <a href="/configuration/clients-beneficiaires" className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                        <span className="mr-3">👤</span>
                        Clients Bénéficiaires
                      </a>
                      <a href="/configuration/secteurs-activite" className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                        <span className="mr-3">🏷️</span>
                        Secteurs d'Activité
                      </a>
                      <a href="/configuration/projets" className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                        <span className="mr-3">📁</span>
                        Projets
                      </a>
                    </div>
                  </div>
                </nav>
                <div className="p-4 border-t">
                  <p className="text-xs text-muted-foreground">
                    © GesGar {new Date().getFullYear()}
                  </p>
                </div>
              </aside>
                {/* Main content */}
                <div className="flex-1 pt-16">
                  {children}
                </div>
              </div>
            </div>
          ) : (
            // Layout simple pour les pages d'authentification
            <div className="min-h-screen flex items-center justify-center bg-white">
              {children}
            </div>
          )}
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}