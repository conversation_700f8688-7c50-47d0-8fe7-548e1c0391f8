// src/app/api/configuration/liaisons-partenaires/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { RoleUtilisateur } from '@prisma/client';

// GET - Lister toutes les liaisons utilisateur-partenaire
export async function GET() {
  const session = await getServerSession(authOptions);
  
  // Vérifier les permissions
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  try {
    // Récupérer tous les utilisateurs avec le rôle Partenaire
    const utilisateursPartenaires = await prisma.utilisateur.findMany({
      where: {
        role: RoleUtilisateur.Partenaire,
        estActif: true
      },
      select: {
        id: true,
        nomUtilisateur: true,
        email: true,
        nom: true,
        prenom: true,
        partenaireAssocie: {
          select: {
            id: true,
            nom: true,
            typePartenaire: true,
            contact: true
          }
        }
      },
      orderBy: [
        { nom: 'asc' },
        { prenom: 'asc' }
      ]
    });

    // Récupérer tous les partenaires disponibles
    const partenairesDisponibles = await prisma.partenaire.findMany({
      where: {
        utilisateurPartenaireId: null // Partenaires non encore liés
      },
      select: {
        id: true,
        nom: true,
        typePartenaire: true,
        contact: true
      },
      orderBy: { nom: 'asc' }
    });

    // Formater les données pour l'interface
    const liaisons = utilisateursPartenaires.map(utilisateur => ({
      utilisateur: {
        id: utilisateur.id,
        nomUtilisateur: utilisateur.nomUtilisateur,
        email: utilisateur.email,
        nom: utilisateur.nom,
        prenom: utilisateur.prenom,
        nomComplet: `${utilisateur.prenom} ${utilisateur.nom}`
      },
      partenaire: utilisateur.partenaireAssocie ? {
        id: utilisateur.partenaireAssocie.id,
        nom: utilisateur.partenaireAssocie.nom,
        typePartenaire: utilisateur.partenaireAssocie.typePartenaire,
        contact: utilisateur.partenaireAssocie.contact
      } : null,
      estLie: !!utilisateur.partenaireAssocie
    }));

    return NextResponse.json({
      liaisons,
      partenairesDisponibles,
      total: liaisons.length,
      totalLies: liaisons.filter(l => l.estLie).length,
      totalNonLies: liaisons.filter(l => !l.estLie).length
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des liaisons:', error);
    return NextResponse.json(
      { message: "Erreur lors de la récupération des liaisons" },
      { status: 500 }
    );
  }
}

// POST - Créer une liaison utilisateur-partenaire
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  // Vérifier les permissions
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  try {
    const { utilisateurId, partenaireId } = await request.json();

    // Validation des données
    if (!utilisateurId || !partenaireId) {
      return NextResponse.json(
        { message: "ID utilisateur et ID partenaire requis" },
        { status: 400 }
      );
    }

    // Vérifier que l'utilisateur existe et a le rôle Partenaire
    const utilisateur = await prisma.utilisateur.findUnique({
      where: { id: parseInt(utilisateurId) },
      select: { id: true, role: true, partenaireAssocie: true }
    });

    if (!utilisateur) {
      return NextResponse.json(
        { message: "Utilisateur non trouvé" },
        { status: 404 }
      );
    }

    if (utilisateur.role !== RoleUtilisateur.Partenaire) {
      return NextResponse.json(
        { message: "L'utilisateur doit avoir le rôle Partenaire" },
        { status: 400 }
      );
    }

    if (utilisateur.partenaireAssocie) {
      return NextResponse.json(
        { message: "Cet utilisateur est déjà lié à un partenaire" },
        { status: 400 }
      );
    }

    // Vérifier que le partenaire existe et n'est pas déjà lié
    const partenaire = await prisma.partenaire.findUnique({
      where: { id: parseInt(partenaireId) },
      select: { id: true, nom: true, utilisateurPartenaireId: true }
    });

    if (!partenaire) {
      return NextResponse.json(
        { message: "Partenaire non trouvé" },
        { status: 404 }
      );
    }

    if (partenaire.utilisateurPartenaireId) {
      return NextResponse.json(
        { message: "Ce partenaire est déjà lié à un utilisateur" },
        { status: 400 }
      );
    }

    // Créer la liaison
    const partenaireModifie = await prisma.partenaire.update({
      where: { id: parseInt(partenaireId) },
      data: {
        utilisateurPartenaireId: parseInt(utilisateurId),
        utilisateurModificationId: parseInt(session.user.id),
        dateModification: new Date()
      },
      include: {
        utilisateurPartenaire: {
          select: {
            id: true,
            nomUtilisateur: true,
            email: true,
            nom: true,
            prenom: true
          }
        }
      }
    });

    return NextResponse.json({
      message: "Liaison créée avec succès",
      liaison: {
        utilisateur: partenaireModifie.utilisateurPartenaire,
        partenaire: {
          id: partenaireModifie.id,
          nom: partenaireModifie.nom
        }
      }
    });

  } catch (error) {
    console.error('Erreur lors de la création de la liaison:', error);
    return NextResponse.json(
      { message: "Erreur lors de la création de la liaison" },
      { status: 500 }
    );
  }
}
