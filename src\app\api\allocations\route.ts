// src/app/api/allocations/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
// Assure-toi que ces enums existent dans schema.prisma et que tu as bien fait `npx prisma generate` !
import { RoleUtilisateur, StatutAllocation, Periodicite, StatutLigneGarantie } from "@prisma/client";
import { AllocationLignePartenaireSchema } from "@/lib/schemas/allocation-ligne-partenaire.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
// import sql from "sql-template-strings"; // Not needed with Prisma's $queryRaw
import Decimal from "decimal.js";

// GET: Lister toutes les allocations
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  const userRole = session?.user?.role as string;
  const allowedRoles = ["Administrateur", "GestionnaireGesGar", "AnalysteFinancier", "Partenaire"];
  if (!session || !allowedRoles.includes(userRole)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const statutFilter = searchParams.get("statut"); // Récupérer le paramètre statut

  const whereClause: any = {};

  // Filtrage par statut si le paramètre est fourni
  if (statutFilter) {
    const statutsArray = statutFilter.split(',').map(s => s.trim()).filter(s => Object.values(StatutAllocation).includes(s as StatutAllocation));
    if (statutsArray.length > 0) {
      whereClause.statut = { in: statutsArray as StatutAllocation[] };
    } else {
      // Si le filtre statut est fourni mais invalide, ne rien retourner ou retourner une erreur ?
      // Pour l'instant, si invalide, on ne filtre pas par statut.
      console.warn("Filtre statut invalide pour les allocations:", statutFilter);
    }
  }

  // Filtrage pour le rôle Partenaire - ne voir que ses propres allocations
  if (session.user?.role === RoleUtilisateur.Partenaire) {
    // Récupérer les informations de l'utilisateur connecté
    const utilisateur = await prisma.utilisateur.findUnique({
      where: { id: parseInt(session.user.id) },
      select: { email: true, nom: true, prenom: true }
    });

    if (!utilisateur) {
      return NextResponse.json({ message: "Utilisateur non trouvé" }, { status: 403 });
    }

    // Trouver le partenaire associé à cet utilisateur par email de contact
    const partenaire = await prisma.partenaire.findFirst({
      where: {
        contact: {
          path: ['email'],
          equals: utilisateur.email
        }
      }
    });

    if (!partenaire) {
      console.error(`[SECURITE] Utilisateur Partenaire sans lien Partenaire: email=${utilisateur.email}`);
      return NextResponse.json({ message: "Aucun partenaire associé à cet utilisateur" }, { status: 403 });
    }
    whereClause.partenaireId = partenaire.id;
  }

  try {
    const allocations = await prisma.allocationLignePartenaire.findMany({
      where: whereClause, // Appliquer le filtre
      orderBy: { dateCreation: "desc" },
      include: {
        ligneGarantie: { select: { id: true, nom: true, devise: true, montantDisponible: true } }, // montantDisponible de la ligne est utile ici
        partenaire: { select: { id: true, nom: true } },
        utilisateurCreation: { select: { nomUtilisateur: true }},
      }
    });
    return NextResponse.json(allocations);
  } catch (error) {
    console.error("Erreur GET /api/allocations:", error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// POST: Créer une nouvelle allocation
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: creatorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      // Convertir les dates string en objets Date avant la validation Zod
      if (body.dateAllocation) body.dateAllocation = new Date(body.dateAllocation);
      if (body.dateExpiration) body.dateExpiration = new Date(body.dateExpiration);
      // Pour les champs optionnels de date, s'ils sont envoyés comme "" ou null, Zod les gérera.

      const validation = AllocationLignePartenaireSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        ligneGarantieId, partenaireId, referenceConvention, montantAlloueStr,
        dateAllocation, dateExpiration, statut, tauxCouvertureMaxStr,
        tauxInteretStr, tauxCommissionStr, periodicitePaiementInteret,
        periodicitePaiementCommission, commentaires
      } = validation.data;

      const ligneGarantieIdNum = parseInt(ligneGarantieId);
      const partenaireIdNum = parseInt(partenaireId);
      const montantAlloueDecimal = new Decimal(montantAlloueStr.replace(',', '.'));

      // --- Début de la logique transactionnelle ---
      const result = await prisma.$transaction(async (tx) => {
        // 1. Récupérer la ligne de garantie parente AVEC VERROUILLAGE (SELECT ... FOR UPDATE)
        const rawResult = await tx.$queryRawUnsafe(
          `SELECT * FROM "LigneGarantie" WHERE id = $1 FOR UPDATE`,
          ligneGarantieIdNum
        ) as any[];
        const ligneGarantie = rawResult[0];

        if (!ligneGarantie) {
          throw new Error("Ligne de garantie parente non trouvée.");
        }
        if (ligneGarantie.statut !== StatutLigneGarantie.Active && ligneGarantie.statut !== StatutLigneGarantie.EnAttenteValidation && ligneGarantie.statut !== StatutLigneGarantie.Epuisee) {
            throw new Error(`La ligne de garantie '${ligneGarantie.nom}' n'est pas dans un statut permettant de nouvelles allocations (Statut: ${ligneGarantie.statut}).`);
        }

        // 2. Vérifier la disponibilité sur la ligne de garantie
        if (montantAlloueDecimal.greaterThan(ligneGarantie.montantDisponible)) {
          throw new Error(`Montant alloué (${montantAlloueDecimal}) dépasse le montant disponible (${ligneGarantie.montantDisponible}) sur la ligne de garantie.`);
        }

        // 3. Vérifier l'existence du partenaire
        const partenaireExists = await tx.partenaire.findUnique({ where: { id: partenaireIdNum } });
        if (!partenaireExists) {
          throw new Error("Partenaire non valide.");
        }

        // 4. Créer l'allocation
        const newAllocation = await tx.allocationLignePartenaire.create({
          data: {
            ligneGarantieId: ligneGarantieIdNum,
            partenaireId: partenaireIdNum,
            referenceConvention,
            montantAlloue: montantAlloueDecimal.toString(),
            montantDisponible: montantAlloueDecimal.toString(), // Initialement, tout est disponible sur l'allocation
            dateAllocation,
            dateExpiration: dateExpiration || null, // Zod gère undefined, Prisma attend null si optionnel
            statut: statut as StatutAllocation,
            tauxCouvertureMax: tauxCouvertureMaxStr ? new Decimal(tauxCouvertureMaxStr.replace(',', '.')).toString() : null,
            tauxInteret: tauxInteretStr ? new Decimal(tauxInteretStr.replace(',', '.')).toString() : null,
            tauxCommission: tauxCommissionStr ? new Decimal(tauxCommissionStr.replace(',', '.')).toString() : null,
            periodicitePaiementInteret: periodicitePaiementInteret ? periodicitePaiementInteret as Periodicite : null,
            periodicitePaiementCommission: periodicitePaiementCommission ? periodicitePaiementCommission as Periodicite : null,
            commentaires,
            utilisateurCreationId: creatorId,
          },
        });

        // 5. Mettre à jour le montant disponible de la ligne de garantie parente
        const disponibleDecimal = new Decimal(ligneGarantie.montantDisponible);
const nouveauMontantDisponibleLigne = disponibleDecimal.minus(montantAlloueDecimal);
        await tx.ligneGarantie.update({
          where: { id: ligneGarantieIdNum },
          data: {
            montantDisponible: nouveauMontantDisponibleLigne.toString(),
            utilisateurModificationId: creatorId, // La ligne est modifiée par cette action
          },
        });

        return newAllocation;
      });
      // --- Fin de la logique transactionnelle ---

      return NextResponse.json(result, { status: 201 });

    } catch (error: any) {
      // Les erreurs levées dans la transaction sont attrapées ici
      console.error("Erreur POST /api/allocations:", error);
      if (error.message.includes("dépasse le montant disponible") || error.message.includes("non trouvée") || error.message.includes("non valide")) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      return NextResponse.json({ message: "Erreur interne du serveur lors de la création de l'allocation." }, { status: 500 });
    }
  });
}