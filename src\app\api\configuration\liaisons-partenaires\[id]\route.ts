// src/app/api/configuration/liaisons-partenaires/[id]/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// DELETE - Supprimer une liaison utilisateur-partenaire
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  
  // Vérifier les permissions
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  try {
    const partenaireId = parseInt(params.id);

    if (isNaN(partenaireId)) {
      return NextResponse.json(
        { message: "ID partenaire invalide" },
        { status: 400 }
      );
    }

    // Vérifier que le partenaire existe et est lié à un utilisateur
    const partenaire = await prisma.partenaire.findUnique({
      where: { id: partenaireId },
      select: {
        id: true,
        nom: true,
        utilisateurPartenaireId: true,
        utilisateurPartenaire: {
          select: {
            id: true,
            nomUtilisateur: true,
            email: true,
            nom: true,
            prenom: true
          }
        }
      }
    });

    if (!partenaire) {
      return NextResponse.json(
        { message: "Partenaire non trouvé" },
        { status: 404 }
      );
    }

    if (!partenaire.utilisateurPartenaireId) {
      return NextResponse.json(
        { message: "Ce partenaire n'est lié à aucun utilisateur" },
        { status: 400 }
      );
    }

    // Supprimer la liaison
    const partenaireModifie = await prisma.partenaire.update({
      where: { id: partenaireId },
      data: {
        utilisateurPartenaireId: null,
        utilisateurModificationId: parseInt(session.user.id),
        dateModification: new Date()
      }
    });

    return NextResponse.json({
      message: "Liaison supprimée avec succès",
      ancienneLiaison: {
        utilisateur: partenaire.utilisateurPartenaire,
        partenaire: {
          id: partenaire.id,
          nom: partenaire.nom
        }
      }
    });

  } catch (error) {
    console.error('Erreur lors de la suppression de la liaison:', error);
    return NextResponse.json(
      { message: "Erreur lors de la suppression de la liaison" },
      { status: 500 }
    );
  }
}
