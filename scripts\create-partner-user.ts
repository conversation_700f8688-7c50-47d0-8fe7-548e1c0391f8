// scripts/create-partner-user.ts
import { PrismaClient, RoleUtilisateur, TypePartenaire } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  const partnerEmail = '<EMAIL>';
  const partnerPassword = 'Partner123!';
  
  try {
    // Vérifier si l'utilisateur partenaire existe déjà
    const existingUser = await prisma.utilisateur.findUnique({
      where: { email: partnerEmail }
    });

    if (existingUser) {
      console.log(`L'utilisateur partenaire ${partnerEmail} existe déjà.`);
      return;
    }

    // Créer l'utilisateur partenaire
    const hashedPassword = await bcrypt.hash(partnerPassword, 10);
    const partnerUser = await prisma.utilisateur.create({
      data: {
        nomUtilisateur: 'partenaire_test',
        email: partnerEmail,
        motDePasse: hashedPassword,
        nom: 'Test',
        prenom: 'Partenaire',
        role: RoleUtilisateur.Partenaire,
        estActif: true,
      },
    });

    console.log(`Utilisateur partenaire créé: ${partnerUser.email}`);

    // Créer l'enregistrement partenaire associé
    const partenaire = await prisma.partenaire.create({
      data: {
        nom: 'Banque Test Partenaire',
        typePartenaire: TypePartenaire.Banque,
        description: 'Banque de test pour les fonctionnalités partenaire',
        contact: {
          nomRepresentant: 'Partenaire Test',
          email: partnerEmail, // Même email que l'utilisateur pour la liaison
          telephone: '+221 77 123 45 67',
          adresse: '123 Rue Test, Dakar'
        },
        convention: 'CONV-TEST-001',
        utilisateurCreationId: partnerUser.id, // L'utilisateur se crée lui-même pour le test
      },
    });

    console.log(`Partenaire créé: ${partenaire.nom} (ID: ${partenaire.id})`);
    console.log(`Liaison établie via email: ${partnerEmail}`);
    console.log(`Mot de passe: ${partnerPassword}`);
    
  } catch (error) {
    console.error('Erreur lors de la création du partenaire de test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
