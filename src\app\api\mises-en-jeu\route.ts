// src/app/api/mises-en-jeu/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Ajustez le chemin si nécessaire
import { RoleUtilisateur, StatutMiseEnJeu } from "@prisma/client";

// GET: Lister les demandes de mise en jeu
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  // Permissions: Admin, GestionnaireGesGar, AnalysteFinancier et Partenaires peuvent voir les mises en jeu
  if (!session || ![
      "Administrateur",
      "GestionnaireGesGar",
      "AnalysteFinancier",
      "Partenaire"
    ].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "15"); // Nombre d'éléments par page
  const statutFilter = searchParams.get("statut"); // Pour filtrer par statut(s) spécifique(s)
  const garantieRefFilter = searchParams.get("garantieRef"); // Pour rechercher par référence de garantie
  const partenaireNomFilter = searchParams.get("partenaireNom"); // Pour rechercher par nom de partenaire

  const skip = (page - 1) * limit;

  const whereClause: any = {};

  // Filtrage pour le rôle Partenaire - ne voir que ses propres mises en jeu
  if (session.user?.role === "Partenaire") {
    // Trouver le partenaire associé à cet utilisateur
    const partenaire = await prisma.partenaire.findFirst({
      where: { utilisateurCreationId: parseInt(session.user.id) }
    });
    if (!partenaire) {
      console.error(`[SECURITE] Utilisateur Partenaire sans lien Partenaire: id=${session.user.id}`);
      return NextResponse.json({ message: "Aucun partenaire associé à cet utilisateur" }, { status: 403 });
    }
    whereClause.garantie = {
      partenaireId: partenaire.id
    };
  }

  // Filtre par statut
  if (statutFilter) {
    const statutsArray = statutFilter.split(',').map(s => s.trim()).filter(s => Object.values(StatutMiseEnJeu).includes(s as StatutMiseEnJeu));
    if (statutsArray.length > 0) {
      whereClause.statut = { in: statutsArray as StatutMiseEnJeu[] };
    }
  } else {
    // Par défaut, afficher celles qui nécessitent une action ou sont en cours d'instruction
    // Only show items requiring immediate action by default
    whereClause.statut = { in: [StatutMiseEnJeu.Demandee, StatutMiseEnJeu.EnCoursInstruction] };
  }

  // Filtre par référence de garantie (si la recherche est implémentée côté client)
  if (garantieRefFilter) {
    whereClause.garantie = {
      referenceGarantie: { contains: garantieRefFilter, mode: 'insensitive' }
    };
  }

  // Filtre par nom de partenaire (si la recherche est implémentée côté client)
  if (partenaireNomFilter) {
    whereClause.garantie = {
      ...whereClause.garantie, // Conserver le filtre sur referenceGarantie s'il existe
      allocation: {
        partenaire: {
          nom: { contains: partenaireNomFilter, mode: 'insensitive' }
        }
      }
    };
  }


  try {
    const misesEnJeu = await prisma.miseEnJeu.findMany({
      where: whereClause,
      orderBy: { dateDemande: "asc" }, // Les plus anciennes demandes en premier
      skip: skip,
      take: limit,
      include: {
        garantie: {
          select: {
            id: true,
            referenceGarantie: true,
            montantGarantie: true, // Utile pour comparer avec montantDemande/Approuve
            projet: {
              select: {
                clientBeneficiaire: { select: { nomOuRaisonSociale: true } }
              }
            },
            allocation: {
              select: {
                partenaire: { select: { nom: true } },
                ligneGarantie: { select: { devise: true } } // Pour la devise des montants
              }
            },
          }
        },
        utilisateurCreation: { select: { nomUtilisateur: true, nom: true, prenom: true } }, // Qui a demandé
        utilisateurModification: { select: { nomUtilisateur: true, nom: true, prenom: true } }, // Qui a traité
      }
    });

    const totalMisesEnJeu = await prisma.miseEnJeu.count({ where: whereClause });

    // Fonction de sérialisation pour Decimal -> number
    const serializeMiseEnJeu = (m: any) => ({
      ...m,
      montantDemande: m.montantDemande && typeof m.montantDemande.toNumber === 'function'
        ? m.montantDemande.toNumber()
        : m.montantDemande,
      montantApprouve: m.montantApprouve && typeof m.montantApprouve.toNumber === 'function'
        ? m.montantApprouve.toNumber()
        : m.montantApprouve,
      garantie: m.garantie ? {
        ...m.garantie,
        montantGarantie: m.garantie.montantGarantie && typeof m.garantie.montantGarantie.toNumber === 'function'
          ? m.garantie.montantGarantie.toNumber()
          : m.garantie.montantGarantie,
      } : undefined,
    });
    const data = misesEnJeu.map(serializeMiseEnJeu);

    return NextResponse.json({
      data,
      totalPages: Math.ceil(totalMisesEnJeu / limit),
      currentPage: page,
      totalRecords: totalMisesEnJeu,
    });

  } catch (error) {
    console.error("Erreur GET /api/mises-en-jeu:", error);
    return NextResponse.json({ message: "Erreur interne du serveur lors de la récupération des mises en jeu." }, { status: 500 });
  }
}

// La fonction POST pour créer une mise en jeu est déjà dans /api/garanties/[id]/mises-en-jeu/route.ts
// Les fonctions PUT et DELETE pour une mise en jeu spécifique seront dans /api/mises-en-jeu/[id]/route.ts