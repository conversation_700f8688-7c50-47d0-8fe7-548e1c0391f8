import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutMainlevee, Prisma } from "@prisma/client";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  const allowedRoles = ["Administrateur", "GestionnaireGesGar", "Partenaire"];
  if (!session || !allowedRoles.includes(session.user?.role)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const statutFilter = searchParams.get("statuts") || searchParams.get("statut"); // Support both 'statuts' and 'statut'
  const page = Math.max(1, parseInt(searchParams.get("page") || "1") || 1);
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get("limit") || "15") || 15));
  const skip = (page - 1) * limit;

  const whereClause: Prisma.MainleveeWhereInput = {};

  // Filtrage pour le rôle Partenaire - ne voir que ses propres mainlevées
  if (session.user?.role === RoleUtilisateur.Partenaire) {
    // Récupérer les informations de l'utilisateur connecté
    const utilisateur = await prisma.utilisateur.findUnique({
      where: { id: parseInt(session.user.id) },
      select: { email: true, nom: true, prenom: true }
    });

    if (!utilisateur) {
      return NextResponse.json({ message: "Utilisateur non trouvé" }, { status: 403 });
    }

    // Trouver le partenaire associé à cet utilisateur par email de contact
    const partenaire = await prisma.partenaire.findFirst({
      where: {
        contact: {
          path: ['email'],
          equals: utilisateur.email
        }
      }
    });

    if (!partenaire) {
      console.error(`[SECURITE] Utilisateur Partenaire sans lien Partenaire: email=${utilisateur.email}`);
      return NextResponse.json({ message: "Aucun partenaire associé à cet utilisateur" }, { status: 403 });
    }
    whereClause.garantie = {
      partenaireId: partenaire.id
    };
  }

  if (statutFilter) {
    const statutsArray = statutFilter.split(',').map(s => s.trim()).filter(s => Object.values(StatutMainlevee).includes(s as StatutMainlevee));
    if (statutsArray.length > 0) {
      whereClause.statut = { in: statutsArray as StatutMainlevee[] };
    }
  } else {
    // Par défaut, récupérer seulement les demandes en attente de traitement
    whereClause.statut = { in: [StatutMainlevee.Demandee, StatutMainlevee.EnCoursApprobation] };
  }

  try {
    const mainlevees = await prisma.mainlevee.findMany({
      where: whereClause,
      orderBy: { dateDemande: "asc" },
      skip: skip,
      take: limit,
      include: {
        garantie: {
          select: {
            id: true,
            referenceGarantie: true,
            projet: { select: { clientBeneficiaire: { select: { nomOuRaisonSociale: true } } } },
            allocation: { select: { partenaire: { select: { nom: true } } } },
          }
        },
        utilisateurCreation: { select: { nomUtilisateur: true, nom: true, prenom: true } },
      }
    });

    const totalMainlevees = await prisma.mainlevee.count({ where: whereClause });

    return NextResponse.json({
      data: mainlevees,
      totalPages: Math.ceil(totalMainlevees / limit),
      currentPage: page,
      totalRecords: totalMainlevees,
    });

  } catch (error) {
    console.error("Erreur GET /api/mainlevees:", error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}